import { Request, Response } from 'express';
import { AuthenticatedFrontendRequest } from '../middlewares/frontendAuth';

export class FrontendController {
  // Authentication pages
  static renderLogin(req: AuthenticatedFrontendRequest, res: Response): void {
    res.render('pages/login', {
      title: 'Login',
      user: null,
    });
  }

  static renderRegister(req: AuthenticatedFrontendRequest, res: Response): void {
    res.render('pages/register', {
      title: 'Register',
      user: null,
    });
  }

  // Dashboard
  static renderDashboard(req: AuthenticatedFrontendRequest, res: Response): void {
    res.render('pages/dashboard', {
      title: 'Dashboard',
      user: req.user,
    });
  }

  // Transaction pages
  static renderTransactions(req: AuthenticatedFrontendRequest, res: Response): void {
    const { category } = req.params;
    const categoryConfig = getCategoryConfig(category || '');

    if (!categoryConfig) {
      res.status(404).render('pages/404', {
        title: 'Not Found',
        user: req.user
      });
      return;
    }

    res.render('pages/transactions', {
      title: categoryConfig.title,
      user: req.user,
      categoryName: category,
      categoryTitle: categoryConfig.title,
      categoryIcon: categoryConfig.icon,
    });
  }

  static renderTransactionForm(req: AuthenticatedFrontendRequest, res: Response): void {
    const { category, id } = req.params;
    const categoryConfig = getCategoryConfig(category || '');

    if (!categoryConfig) {
      res.status(404).render('pages/404', {
        title: 'Not Found',
        user: req.user
      });
      return;
    }

    const isEdit = !!id;
    const title = `${isEdit ? 'Edit' : 'Add'} ${categoryConfig.title.slice(0, -1)}`;

    res.render('pages/transaction-form', {
      title,
      user: req.user,
      categoryName: category,
      categoryTitle: categoryConfig.title,
      categoryIcon: categoryConfig.icon,
      isEdit,
      transaction: isEdit ? {
        _id: id,
        amount: 100,
        description: 'Sample transaction',
        date: new Date()
      } : null,
    });
  }

  // Error pages
  static render404(req: AuthenticatedFrontendRequest, res: Response): void {
    res.status(404).render('pages/404', {
      title: 'Page Not Found',
      user: req.user,
    });
  }

  static render500(req: AuthenticatedFrontendRequest, res: Response): void {
    res.status(500).render('pages/500', {
      title: 'Server Error',
      user: req.user,
    });
  }
}

function getCategoryConfig(category: string) {
  const configs: Record<string, { title: string; icon: string }> = {
    incomes: { title: 'Incomes', icon: 'fas fa-money-bill-wave' },
    needs: { title: 'Needs', icon: 'fas fa-shopping-basket' },
    wants: { title: 'Wants', icon: 'fas fa-gift' },
    investments: { title: 'Investments', icon: 'fas fa-chart-line' },
    donations: { title: 'Donations', icon: 'fas fa-heart' },
  };
  
  return configs[category] || null;
}
