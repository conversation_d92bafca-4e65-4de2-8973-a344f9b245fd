// Global utility functions and API helpers

// API Base URL
const API_BASE = '/api';

// Get user data from localStorage (for client-side display)
function getUser() {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
}

// Check if user is authenticated (cookies are handled server-side)
function isAuthenticated() {
    // Since we're using HTTP-only cookies, we can't check auth status from client-side
    // The server will handle redirects for protected routes
    // For client-side checks, we can use the presence of user data
    return !!getUser();
}

// Redirect to login if not authenticated
function requireAuth() {
    if (!isAuthenticated()) {
        window.location.href = '/login';
        return false;
    }
    return true;
}

// Make authenticated API request
async function apiRequest(url, options = {}) {
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json'
        },
        credentials: 'include' // Include cookies in requests
    };

    const mergedOptions = {
        ...defaultOptions,
        ...options,
        headers: {
            ...defaultOptions.headers,
            ...options.headers
        }
    };

    try {
        const response = await fetch(API_BASE + url, mergedOptions);

        // Handle authentication errors
        if (response.status === 401) {
            // Server will handle token refresh automatically
            // If we get 401, it means authentication failed completely
            logout();
            return response;
        }

        return response;
    } catch (error) {
        console.error('API request failed:', error);
        throw error;
    }
}

// Logout user
async function logout() {
    try {
        // Call logout API to clear server-side session
        await fetch(API_BASE + '/auth/logout', {
            method: 'POST',
            credentials: 'include'
        });
    } catch (error) {
        console.error('Logout API call failed:', error);
    }

    // Clear client-side data
    localStorage.removeItem('user');
    window.location.href = '/login';
}

// Show alert message
function showAlert(type, message, container = null) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="fas fa-${getAlertIcon(type)} me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    if (container) {
        container.innerHTML = alertHtml + container.innerHTML;
    } else {
        // Add to top of main content
        const main = document.querySelector('main');
        if (main) {
            main.insertAdjacentHTML('afterbegin', alertHtml);
        }
    }
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        const alert = document.querySelector('.alert');
        if (alert) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }
    }, 5000);
}

// Get icon for alert type
function getAlertIcon(type) {
    const icons = {
        success: 'check-circle',
        error: 'exclamation-circle',
        warning: 'exclamation-triangle',
        info: 'info-circle',
        danger: 'exclamation-circle'
    };
    return icons[type] || 'info-circle';
}

// Format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

// Format date
function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

// Format percentage
function formatPercentage(value) {
    return `${value.toFixed(1)}%`;
}

// Debounce function
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Authentication is now handled server-side with middleware
    // No need for client-side auth checks as server redirects appropriately

    // Auto-dismiss alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
    alerts.forEach(alert => {
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });
});

// Handle logout
document.addEventListener('click', function(e) {
    if (e.target.closest('a[href="/logout"]')) {
        e.preventDefault();
        logout();
    }
});
