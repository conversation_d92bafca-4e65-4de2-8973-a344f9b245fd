// Global utility functions and API helpers

// API Base URL
const API_BASE = '/api';

// Get user data from localStorage (for client-side display)
function getUser() {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
}

// Check if user is authenticated (cookies are handled server-side)
function isAuthenticated() {
    // Since we're using HTTP-only cookies, we can't check auth status from client-side
    // The server will handle redirects for protected routes
    // For client-side checks, we can use the presence of user data
    return !!getUser();
}

// Redirect to login if not authenticated
function requireAuth() {
    if (!isAuthenticated()) {
        window.location.href = '/login';
        return false;
    }
    return true;
}

// Track ongoing refresh attempts to prevent multiple simultaneous refreshes
let isRefreshing = false;
let refreshPromise = null;

// Make authenticated API request
async function apiRequest(url, options = {}) {
    const user = getUser();
    const tokens = user ? JSON.parse(localStorage.getItem('tokens') || '{}') : {};

    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json'
        },
        credentials: 'include' // Include cookies in requests
    };

    // Add Authorization header if we have an access token
    if (tokens.accessToken) {
        defaultOptions.headers['Authorization'] = `Bearer ${tokens.accessToken}`;
    }

    const mergedOptions = {
        ...defaultOptions,
        ...options,
        headers: {
            ...defaultOptions.headers,
            ...options.headers
        }
    };

    try {
        const response = await fetch(API_BASE + url, mergedOptions);

        // Handle authentication errors
        if (response.status === 401) {
            // Try to refresh token if we have a refresh token
            if (tokens.refreshToken && !isRefreshing) {
                // If already refreshing, wait for that to complete
                if (refreshPromise) {
                    await refreshPromise;
                    // Retry with potentially new token
                    const newTokens = JSON.parse(localStorage.getItem('tokens') || '{}');
                    if (newTokens.accessToken) {
                        mergedOptions.headers['Authorization'] = `Bearer ${newTokens.accessToken}`;
                        return await fetch(API_BASE + url, mergedOptions);
                    }
                } else {
                    // Start refresh process
                    refreshPromise = refreshAccessToken();
                    const refreshed = await refreshPromise;
                    refreshPromise = null;

                    if (refreshed) {
                        // Retry the original request with new token
                        const newTokens = JSON.parse(localStorage.getItem('tokens') || '{}');
                        mergedOptions.headers['Authorization'] = `Bearer ${newTokens.accessToken}`;
                        return await fetch(API_BASE + url, mergedOptions);
                    }
                }
            }

            // If refresh failed or no refresh token, logout
            console.warn('Authentication failed, redirecting to login');
            logout();
            return response;
        }

        return response;
    } catch (error) {
        console.error('API request failed:', error);
        throw error;
    }
}

// Refresh access token using refresh token
async function refreshAccessToken() {
    if (isRefreshing) {
        console.log('Token refresh already in progress');
        return false;
    }

    try {
        isRefreshing = true;
        const tokens = JSON.parse(localStorage.getItem('tokens') || '{}');
        if (!tokens.refreshToken) {
            return false;
        }

        console.log('Attempting to refresh access token...');
        const response = await fetch(API_BASE + '/auth/refresh', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ refreshToken: tokens.refreshToken }),
            credentials: 'include'
        });

        if (response.ok) {
            const data = await response.json();
            localStorage.setItem('tokens', JSON.stringify(data.tokens));
            console.log('Token refresh successful');
            return true;
        }

        console.warn('Token refresh failed with status:', response.status);
        return false;
    } catch (error) {
        console.error('Token refresh failed:', error);
        return false;
    } finally {
        isRefreshing = false;
    }
}

// Logout user
async function logout() {
    try {
        const tokens = JSON.parse(localStorage.getItem('tokens') || '{}');

        // Call logout API to clear server-side session
        await fetch(API_BASE + '/auth/logout', {
            method: 'POST',
            headers: {
                'Authorization': tokens.accessToken ? `Bearer ${tokens.accessToken}` : ''
            },
            credentials: 'include'
        });
    } catch (error) {
        console.error('Logout API call failed:', error);
    }

    // Clear client-side data
    localStorage.removeItem('user');
    localStorage.removeItem('tokens');
    window.location.href = '/login';
}

// Show alert message
function showAlert(type, message, container = null) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="fas fa-${getAlertIcon(type)} me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    if (container) {
        container.innerHTML = alertHtml + container.innerHTML;
    } else {
        // Add to top of main content
        const main = document.querySelector('main');
        if (main) {
            main.insertAdjacentHTML('afterbegin', alertHtml);
        }
    }
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        const alert = document.querySelector('.alert');
        if (alert) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }
    }, 5000);
}

// Get icon for alert type
function getAlertIcon(type) {
    const icons = {
        success: 'check-circle',
        error: 'exclamation-circle',
        warning: 'exclamation-triangle',
        info: 'info-circle',
        danger: 'exclamation-circle'
    };
    return icons[type] || 'info-circle';
}

// Format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

// Format date
function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

// Format percentage
function formatPercentage(value) {
    return `${value.toFixed(1)}%`;
}

// Debounce function
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Authentication is now handled server-side with middleware
    // No need for client-side auth checks as server redirects appropriately

    // Auto-dismiss alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
    alerts.forEach(alert => {
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });
});

// Handle logout
document.addEventListener('click', function(e) {
    if (e.target.closest('a[href="/logout"]')) {
        e.preventDefault();
        logout();
    }
});
