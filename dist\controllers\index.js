"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserController = exports.AuthController = exports.donationController = exports.investmentController = exports.wantController = exports.needController = exports.incomeController = void 0;
const transactionController_1 = require("./transactionController");
const models_1 = require("../models");
exports.incomeController = new transactionController_1.TransactionController(models_1.Income, 'Income');
exports.needController = new transactionController_1.TransactionController(models_1.Need, 'Need');
exports.wantController = new transactionController_1.TransactionController(models_1.Want, 'Want');
exports.investmentController = new transactionController_1.TransactionController(models_1.Investment, 'Investment');
exports.donationController = new transactionController_1.TransactionController(models_1.Donation, 'Donation');
var authController_1 = require("./authController");
Object.defineProperty(exports, "AuthController", { enumerable: true, get: function () { return authController_1.AuthController; } });
var userController_1 = require("./userController");
Object.defineProperty(exports, "UserController", { enumerable: true, get: function () { return userController_1.UserController; } });
//# sourceMappingURL=index.js.map