import { Request, Response, NextFunction } from 'express';
export interface AuthenticatedFrontendRequest extends Request {
    user?: any;
    isAuthenticated?: boolean;
}
export declare const checkFrontendAuth: (req: AuthenticatedFrontendRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const requireFrontendAuth: (req: AuthenticatedFrontendRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const redirectIfAuthenticated: (req: AuthenticatedFrontendRequest, res: Response, next: NextFunction) => Promise<void>;
//# sourceMappingURL=frontendAuth.d.ts.map