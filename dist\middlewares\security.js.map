{"version": 3, "file": "security.js", "sourceRoot": "", "sources": ["../../src/middlewares/security.ts"], "names": [], "mappings": ";;;;;;AAAA,4EAA2C;AAC3C,oFAAmD;AACnD,oDAA4B;AAC5B,gDAAwB;AACxB,uCAAuC;AAGhC,MAAM,eAAe,GAAG,CAAC,QAAiB,EAAE,GAAY,EAAE,EAAE;IACjE,OAAO,IAAA,4BAAS,EAAC;QACf,QAAQ,EAAE,QAAQ,IAAI,YAAM,CAAC,oBAAoB;QACjD,GAAG,EAAE,GAAG,IAAI,YAAM,CAAC,uBAAuB;QAC1C,OAAO,EAAE;YACP,KAAK,EAAE,yDAAyD;SACjE;QACD,eAAe,EAAE,IAAI;QACrB,aAAa,EAAE,KAAK;KACrB,CAAC,CAAC;AACL,CAAC,CAAC;AAVW,QAAA,eAAe,mBAU1B;AAGW,QAAA,aAAa,GAAG,IAAA,uBAAe,EAC1C,EAAE,GAAG,EAAE,GAAG,IAAI,EACd,YAAM,CAAC,QAAQ,KAAK,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAC3C,CAAC;AAGW,QAAA,YAAY,GAAG,IAAA,uBAAe,EACzC,EAAE,GAAG,EAAE,GAAG,IAAI,EACd,YAAM,CAAC,QAAQ,KAAK,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAC/C,CAAC;AAGW,QAAA,WAAW,GAAG;IACzB,MAAM,EAAE,UAAU,MAA0B,EAAE,QAAkB;QAE9D,IAAI,CAAC,MAAM;YAAE,OAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAGzC,IAAI,YAAM,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YACtC,OAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC9B,CAAC;QAGD,MAAM,cAAc,GAAG;YACrB,uBAAuB;YACvB,uBAAuB;SAExB,CAAC;QAEF,IAAI,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACpC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACvB,CAAC;aAAM,CAAC;YACN,QAAQ,CAAC,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IACD,WAAW,EAAE,IAAI;IACjB,oBAAoB,EAAE,GAAG;CAC1B,CAAC;AAGK,MAAM,aAAa,GAAG,GAAG,EAAE;IAChC,OAAO;QAEL,IAAA,gBAAM,EAAC;YACL,qBAAqB,EAAE;gBACrB,UAAU,EAAE;oBACV,UAAU,EAAE,CAAC,QAAQ,CAAC;oBACtB,QAAQ,EAAE;wBACR,QAAQ;wBACR,iBAAiB;wBACjB,0BAA0B;wBAC1B,8BAA8B;qBAC/B;oBACD,SAAS,EAAE;wBACT,QAAQ;wBACR,iBAAiB;wBACjB,0BAA0B;wBAC1B,8BAA8B;qBAC/B;oBACD,MAAM,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC;oBACrC,OAAO,EAAE;wBACP,QAAQ;wBACR,8BAA8B;wBAC9B,OAAO;qBACR;oBACD,UAAU,EAAE,CAAC,QAAQ,CAAC;iBACvB;aACF;SACF,CAAC;QAGF,IAAA,cAAI,EAAC,mBAAW,CAAC;QAGjB,IAAA,gCAAa,GAAE;KAChB,CAAC;AACJ,CAAC,CAAC;AApCW,QAAA,aAAa,iBAoCxB"}