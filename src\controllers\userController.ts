import { Response } from 'express';
import { AuthenticatedRequest } from '../middlewares/auth';
import { User } from '../models';

export class UserController {
  /**
   * Get current user profile
   */
  static async getProfile(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      // Get fresh user data from database
      const user = await User.findById(req.user.id).select('-passwordHash -devices.refreshToken');
      
      if (!user) {
        res.status(404).json({ error: 'User not found' });
        return;
      }

      res.json({
        user: {
          id: user._id,
          email: user.email,
          fullName: user.fullName,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
          deviceCount: user.devices.length
        }
      });
    } catch (error) {
      console.error('Get profile error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Update user profile
   */
  static async updateProfile(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      const { fullName, email } = req.body;

      if (!fullName && !email) {
        res.status(400).json({ error: 'At least one field (fullName or email) is required' });
        return;
      }

      // Check if email is already taken by another user
      if (email) {
        const existingUser = await User.findOne({ 
          email: email.toLowerCase(), 
          _id: { $ne: req.user.id } 
        });
        
        if (existingUser) {
          res.status(409).json({ error: 'Email is already taken' });
          return;
        }
      }

      // Update user
      const updateData: any = {};
      if (fullName) updateData.fullName = fullName.trim();
      if (email) updateData.email = email.toLowerCase().trim();

      const updatedUser = await User.findByIdAndUpdate(
        req.user.id,
        updateData,
        { new: true, runValidators: true }
      ).select('-passwordHash -devices.refreshToken');

      if (!updatedUser) {
        res.status(404).json({ error: 'User not found' });
        return;
      }

      res.json({
        message: 'Profile updated successfully',
        user: {
          id: updatedUser._id,
          email: updatedUser.email,
          fullName: updatedUser.fullName,
          createdAt: updatedUser.createdAt,
          updatedAt: updatedUser.updatedAt,
          deviceCount: updatedUser.devices.length
        }
      });
    } catch (error) {
      console.error('Update profile error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Get user devices
   */
  static async getDevices(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      const user = await User.findById(req.user.id).select('devices');
      
      if (!user) {
        res.status(404).json({ error: 'User not found' });
        return;
      }

      // Return device info without sensitive data
      const devices = user.devices.map(device => ({
        deviceId: device.deviceId,
        userAgent: device.userAgent,
        ip: device.ip,
        lastSeen: device.lastSeen,
        isCurrent: device.deviceId === req.user.deviceId
      }));

      res.json({
        devices,
        maxDevices: parseInt(process.env.MAX_DEVICES_PER_USER || '3')
      });
    } catch (error) {
      console.error('Get devices error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Remove a specific device
   */
  static async removeDevice(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      const { deviceId } = req.params;

      if (!deviceId) {
        res.status(400).json({ error: 'Device ID is required' });
        return;
      }

      // Don't allow removing current device
      if (deviceId === req.user.deviceId) {
        res.status(400).json({ error: 'Cannot remove current device. Use logout instead.' });
        return;
      }

      const user = await User.findById(req.user.id);
      
      if (!user) {
        res.status(404).json({ error: 'User not found' });
        return;
      }

      // Remove the device
      user.devices = user.devices.filter(device => device.deviceId !== deviceId);
      await user.save();

      res.json({ message: 'Device removed successfully' });
    } catch (error) {
      console.error('Remove device error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
}
