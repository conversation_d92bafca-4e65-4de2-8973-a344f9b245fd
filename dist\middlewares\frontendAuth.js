"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.redirectIfAuthenticated = exports.requireFrontendAuth = exports.checkFrontendAuth = void 0;
const authService_1 = require("../services/authService");
const models_1 = require("../models");
const checkFrontendAuth = async (req, res, next) => {
    try {
        const accessToken = req.cookies?.accessToken;
        if (!accessToken) {
            req.isAuthenticated = false;
            req.user = null;
            next();
            return;
        }
        try {
            const payload = authService_1.AuthService.verifyAccessToken(accessToken);
            const user = await models_1.User.findById(payload.userId);
            if (!user) {
                res.clearCookie('accessToken');
                res.clearCookie('refreshToken');
                req.isAuthenticated = false;
                req.user = null;
                next();
                return;
            }
            const device = user.devices.find(d => d.deviceId === payload.deviceId);
            if (!device) {
                res.clearCookie('accessToken');
                res.clearCookie('refreshToken');
                req.isAuthenticated = false;
                req.user = null;
                next();
                return;
            }
            req.isAuthenticated = true;
            req.user = {
                id: user._id,
                email: user.email,
                fullName: user.fullName,
                deviceId: payload.deviceId
            };
            next();
        }
        catch (tokenError) {
            const refreshToken = req.cookies?.refreshToken;
            if (!refreshToken) {
                res.clearCookie('accessToken');
                req.isAuthenticated = false;
                req.user = null;
                next();
                return;
            }
            try {
                const newTokens = await authService_1.AuthService.refreshTokens(refreshToken);
                res.cookie('accessToken', newTokens.accessToken, {
                    httpOnly: true,
                    secure: process.env.NODE_ENV === 'production',
                    sameSite: 'lax',
                    maxAge: 15 * 60 * 1000
                });
                res.cookie('refreshToken', newTokens.refreshToken, {
                    httpOnly: true,
                    secure: process.env.NODE_ENV === 'production',
                    sameSite: 'lax',
                    maxAge: 7 * 24 * 60 * 60 * 1000
                });
                const newPayload = authService_1.AuthService.verifyAccessToken(newTokens.accessToken);
                const user = await models_1.User.findById(newPayload.userId);
                if (user) {
                    req.isAuthenticated = true;
                    req.user = {
                        id: user._id,
                        email: user.email,
                        fullName: user.fullName,
                        deviceId: newPayload.deviceId
                    };
                }
                else {
                    req.isAuthenticated = false;
                    req.user = null;
                }
                next();
            }
            catch (refreshError) {
                res.clearCookie('accessToken');
                res.clearCookie('refreshToken');
                req.isAuthenticated = false;
                req.user = null;
                next();
            }
        }
    }
    catch (error) {
        console.error('Frontend auth middleware error:', error);
        req.isAuthenticated = false;
        req.user = null;
        next();
    }
};
exports.checkFrontendAuth = checkFrontendAuth;
const requireFrontendAuth = async (req, res, next) => {
    await (0, exports.checkFrontendAuth)(req, res, () => { });
    if (!req.isAuthenticated) {
        res.redirect('/login');
        return;
    }
    next();
};
exports.requireFrontendAuth = requireFrontendAuth;
const redirectIfAuthenticated = async (req, res, next) => {
    await (0, exports.checkFrontendAuth)(req, res, () => { });
    if (req.isAuthenticated) {
        res.redirect('/dashboard');
        return;
    }
    next();
};
exports.redirectIfAuthenticated = redirectIfAuthenticated;
//# sourceMappingURL=frontendAuth.js.map