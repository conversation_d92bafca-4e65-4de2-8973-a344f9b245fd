"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = __importDefault(require("./auth"));
const user_1 = __importDefault(require("./user"));
const dashboard_1 = __importDefault(require("./dashboard"));
const transactions_1 = require("./transactions");
const controllers_1 = require("../controllers");
const router = (0, express_1.Router)();
router.use('/auth', auth_1.default);
router.use('/user', user_1.default);
router.use('/dashboard', dashboard_1.default);
router.use('/incomes', (0, transactions_1.createTransactionRoutes)(controllers_1.incomeController));
router.use('/needs', (0, transactions_1.createTransactionRoutes)(controllers_1.needController));
router.use('/wants', (0, transactions_1.createTransactionRoutes)(controllers_1.wantController));
router.use('/investments', (0, transactions_1.createTransactionRoutes)(controllers_1.investmentController));
router.use('/donations', (0, transactions_1.createTransactionRoutes)(controllers_1.donationController));
exports.default = router;
//# sourceMappingURL=index.js.map