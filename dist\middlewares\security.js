"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupSecurity = exports.corsOptions = exports.apiRateLimit = exports.authRateLimit = exports.createRateLimit = void 0;
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const express_mongo_sanitize_1 = __importDefault(require("express-mongo-sanitize"));
const helmet_1 = __importDefault(require("helmet"));
const cors_1 = __importDefault(require("cors"));
const env_1 = require("../config/env");
const createRateLimit = (windowMs, max) => {
    return (0, express_rate_limit_1.default)({
        windowMs: windowMs || env_1.config.RATE_LIMIT_WINDOW_MS,
        max: max || env_1.config.RATE_LIMIT_MAX_REQUESTS,
        message: {
            error: 'Too many requests from this IP, please try again later.',
        },
        standardHeaders: true,
        legacyHeaders: false,
    });
};
exports.createRateLimit = createRateLimit;
exports.authRateLimit = (0, exports.createRateLimit)(15 * 60 * 1000, 5);
exports.apiRateLimit = (0, exports.createRateLimit)();
exports.corsOptions = {
    origin: function (origin, callback) {
        if (!origin)
            return callback(null, true);
        if (env_1.config.NODE_ENV === 'development') {
            return callback(null, true);
        }
        const allowedOrigins = [
            'http://localhost:3000',
            'http://localhost:3001',
        ];
        if (allowedOrigins.includes(origin)) {
            callback(null, true);
        }
        else {
            callback(new Error('Not allowed by CORS'));
        }
    },
    credentials: true,
    optionsSuccessStatus: 200,
};
const setupSecurity = () => {
    return [
        (0, helmet_1.default)({
            contentSecurityPolicy: {
                directives: {
                    defaultSrc: ["'self'"],
                    styleSrc: [
                        "'self'",
                        "'unsafe-inline'",
                        "https://cdn.jsdelivr.net",
                        "https://cdnjs.cloudflare.com"
                    ],
                    scriptSrc: [
                        "'self'",
                        "'unsafe-inline'",
                        "https://cdn.jsdelivr.net",
                        "https://cdnjs.cloudflare.com"
                    ],
                    imgSrc: ["'self'", "data:", "https:"],
                    fontSrc: [
                        "'self'",
                        "https://cdnjs.cloudflare.com",
                        "data:"
                    ],
                    connectSrc: ["'self'"],
                },
            },
        }),
        (0, cors_1.default)(exports.corsOptions),
        (0, express_mongo_sanitize_1.default)(),
        exports.apiRateLimit,
    ];
};
exports.setupSecurity = setupSecurity;
//# sourceMappingURL=security.js.map