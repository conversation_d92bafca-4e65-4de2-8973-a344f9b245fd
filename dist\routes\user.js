"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const controllers_1 = require("../controllers");
const auth_1 = require("../middlewares/auth");
const validation_1 = require("../middlewares/validation");
const zod_1 = require("zod");
const router = (0, express_1.Router)();
router.use(auth_1.authenticate);
const updateProfileSchema = zod_1.z.object({
    fullName: zod_1.z.string().min(1).max(100).optional(),
    email: zod_1.z.string().email().optional()
}).refine(data => data.fullName || data.email, {
    message: "At least one field (fullName or email) is required"
});
router.get('/profile', controllers_1.UserController.getProfile);
router.put('/profile', (0, validation_1.validate)(updateProfileSchema), controllers_1.UserController.updateProfile);
router.get('/devices', controllers_1.UserController.getDevices);
router.delete('/devices/:deviceId', controllers_1.UserController.removeDevice);
exports.default = router;
//# sourceMappingURL=user.js.map