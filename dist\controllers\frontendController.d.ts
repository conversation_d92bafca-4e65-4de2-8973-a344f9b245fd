import { Response } from 'express';
import { AuthenticatedFrontendRequest } from '../middlewares/frontendAuth';
export declare class FrontendController {
    static renderLogin(req: AuthenticatedFrontendRequest, res: Response): void;
    static renderRegister(req: AuthenticatedFrontendRequest, res: Response): void;
    static renderDashboard(req: AuthenticatedFrontendRequest, res: Response): void;
    static renderTransactions(req: AuthenticatedFrontendRequest, res: Response): void;
    static renderTransactionForm(req: AuthenticatedFrontendRequest, res: Response): void;
    static render404(req: AuthenticatedFrontendRequest, res: Response): void;
    static render500(req: AuthenticatedFrontendRequest, res: Response): void;
}
//# sourceMappingURL=frontendController.d.ts.map