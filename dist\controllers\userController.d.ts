import { Response } from 'express';
import { AuthenticatedRequest } from '../middlewares/auth';
export declare class UserController {
    static getProfile(req: AuthenticatedRequest, res: Response): Promise<void>;
    static updateProfile(req: AuthenticatedRequest, res: Response): Promise<void>;
    static getDevices(req: AuthenticatedRequest, res: Response): Promise<void>;
    static removeDevice(req: AuthenticatedRequest, res: Response): Promise<void>;
}
//# sourceMappingURL=userController.d.ts.map