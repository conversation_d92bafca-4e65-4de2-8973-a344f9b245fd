{"version": 3, "file": "frontendAuth.js", "sourceRoot": "", "sources": ["../../src/middlewares/frontendAuth.ts"], "names": [], "mappings": ";;;AACA,yDAAsD;AACtD,sCAAiC;AAW1B,MAAM,iBAAiB,GAAG,KAAK,EACpC,GAAiC,EACjC,GAAa,EACb,IAAkB,EACH,EAAE;IACjB,IAAI,CAAC;QAEH,MAAM,WAAW,GAAG,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC;QAE7C,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,GAAG,CAAC,eAAe,GAAG,KAAK,CAAC;YAC5B,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;YAChB,IAAI,EAAE,CAAC;YACP,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YAEH,MAAM,OAAO,GAAG,yBAAW,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;YAG3D,MAAM,IAAI,GAAG,MAAM,aAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACjD,IAAI,CAAC,IAAI,EAAE,CAAC;gBAEV,GAAG,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;gBAC/B,GAAG,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;gBAChC,GAAG,CAAC,eAAe,GAAG,KAAK,CAAC;gBAC5B,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;gBAChB,IAAI,EAAE,CAAC;gBACP,OAAO;YACT,CAAC;YAGD,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,OAAO,CAAC,QAAQ,CAAC,CAAC;YACvE,IAAI,CAAC,MAAM,EAAE,CAAC;gBAEZ,GAAG,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;gBAC/B,GAAG,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;gBAChC,GAAG,CAAC,eAAe,GAAG,KAAK,CAAC;gBAC5B,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;gBAChB,IAAI,EAAE,CAAC;gBACP,OAAO;YACT,CAAC;YAGD,GAAG,CAAC,eAAe,GAAG,IAAI,CAAC;YAC3B,GAAG,CAAC,IAAI,GAAG;gBACT,EAAE,EAAE,IAAI,CAAC,GAAG;gBACZ,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,QAAQ,EAAE,OAAO,CAAC,QAAQ;aAC3B,CAAC;YAEF,IAAI,EAAE,CAAC;QACT,CAAC;QAAC,OAAO,UAAU,EAAE,CAAC;YAEpB,MAAM,YAAY,GAAG,GAAG,CAAC,OAAO,EAAE,YAAY,CAAC;YAE/C,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,GAAG,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;gBAC/B,GAAG,CAAC,eAAe,GAAG,KAAK,CAAC;gBAC5B,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;gBAChB,IAAI,EAAE,CAAC;gBACP,OAAO;YACT,CAAC;YAED,IAAI,CAAC;gBAEH,MAAM,SAAS,GAAG,MAAM,yBAAW,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;gBAGhE,GAAG,CAAC,MAAM,CAAC,aAAa,EAAE,SAAS,CAAC,WAAW,EAAE;oBAC/C,QAAQ,EAAE,IAAI;oBACd,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;oBAC7C,QAAQ,EAAE,KAAK;oBACf,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;iBACvB,CAAC,CAAC;gBAEH,GAAG,CAAC,MAAM,CAAC,cAAc,EAAE,SAAS,CAAC,YAAY,EAAE;oBACjD,QAAQ,EAAE,IAAI;oBACd,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;oBAC7C,QAAQ,EAAE,KAAK;oBACf,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;iBAChC,CAAC,CAAC;gBAGH,MAAM,UAAU,GAAG,yBAAW,CAAC,iBAAiB,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;gBACxE,MAAM,IAAI,GAAG,MAAM,aAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;gBAEpD,IAAI,IAAI,EAAE,CAAC;oBACT,GAAG,CAAC,eAAe,GAAG,IAAI,CAAC;oBAC3B,GAAG,CAAC,IAAI,GAAG;wBACT,EAAE,EAAE,IAAI,CAAC,GAAG;wBACZ,KAAK,EAAE,IAAI,CAAC,KAAK;wBACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;wBACvB,QAAQ,EAAE,UAAU,CAAC,QAAQ;qBAC9B,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,GAAG,CAAC,eAAe,GAAG,KAAK,CAAC;oBAC5B,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;gBAClB,CAAC;gBAED,IAAI,EAAE,CAAC;YACT,CAAC;YAAC,OAAO,YAAY,EAAE,CAAC;gBAEtB,GAAG,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;gBAC/B,GAAG,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;gBAChC,GAAG,CAAC,eAAe,GAAG,KAAK,CAAC;gBAC5B,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;gBAChB,IAAI,EAAE,CAAC;YACT,CAAC;QACH,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QACxD,GAAG,CAAC,eAAe,GAAG,KAAK,CAAC;QAC5B,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;QAChB,IAAI,EAAE,CAAC;IACT,CAAC;AACH,CAAC,CAAC;AAtHW,QAAA,iBAAiB,qBAsH5B;AAMK,MAAM,mBAAmB,GAAG,KAAK,EACtC,GAAiC,EACjC,GAAa,EACb,IAAkB,EACH,EAAE;IAEjB,MAAM,IAAA,yBAAiB,EAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IAE5C,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,CAAC;QACzB,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACvB,OAAO;IACT,CAAC;IAED,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAdW,QAAA,mBAAmB,uBAc9B;AAKK,MAAM,uBAAuB,GAAG,KAAK,EAC1C,GAAiC,EACjC,GAAa,EACb,IAAkB,EACH,EAAE;IAEjB,MAAM,IAAA,yBAAiB,EAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IAE5C,IAAI,GAAG,CAAC,eAAe,EAAE,CAAC;QACxB,GAAG,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QAC3B,OAAO;IACT,CAAC;IAED,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAdW,QAAA,uBAAuB,2BAclC"}