"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserController = void 0;
const models_1 = require("../models");
class UserController {
    static async getProfile(req, res) {
        try {
            if (!req.user) {
                res.status(401).json({ error: 'User not authenticated' });
                return;
            }
            const user = await models_1.User.findById(req.user.id).select('-passwordHash -devices.refreshToken');
            if (!user) {
                res.status(404).json({ error: 'User not found' });
                return;
            }
            res.json({
                user: {
                    id: user._id,
                    email: user.email,
                    fullName: user.fullName,
                    createdAt: user.createdAt,
                    updatedAt: user.updatedAt,
                    deviceCount: user.devices.length
                }
            });
        }
        catch (error) {
            console.error('Get profile error:', error);
            res.status(500).json({ error: 'Internal server error' });
        }
    }
    static async updateProfile(req, res) {
        try {
            if (!req.user) {
                res.status(401).json({ error: 'User not authenticated' });
                return;
            }
            const { fullName, email } = req.body;
            if (!fullName && !email) {
                res.status(400).json({ error: 'At least one field (fullName or email) is required' });
                return;
            }
            if (email) {
                const existingUser = await models_1.User.findOne({
                    email: email.toLowerCase(),
                    _id: { $ne: req.user.id }
                });
                if (existingUser) {
                    res.status(409).json({ error: 'Email is already taken' });
                    return;
                }
            }
            const updateData = {};
            if (fullName)
                updateData.fullName = fullName.trim();
            if (email)
                updateData.email = email.toLowerCase().trim();
            const updatedUser = await models_1.User.findByIdAndUpdate(req.user.id, updateData, { new: true, runValidators: true }).select('-passwordHash -devices.refreshToken');
            if (!updatedUser) {
                res.status(404).json({ error: 'User not found' });
                return;
            }
            res.json({
                message: 'Profile updated successfully',
                user: {
                    id: updatedUser._id,
                    email: updatedUser.email,
                    fullName: updatedUser.fullName,
                    createdAt: updatedUser.createdAt,
                    updatedAt: updatedUser.updatedAt,
                    deviceCount: updatedUser.devices.length
                }
            });
        }
        catch (error) {
            console.error('Update profile error:', error);
            res.status(500).json({ error: 'Internal server error' });
        }
    }
    static async getDevices(req, res) {
        try {
            if (!req.user) {
                res.status(401).json({ error: 'User not authenticated' });
                return;
            }
            const user = await models_1.User.findById(req.user.id).select('devices');
            if (!user) {
                res.status(404).json({ error: 'User not found' });
                return;
            }
            const devices = user.devices.map(device => ({
                deviceId: device.deviceId,
                userAgent: device.userAgent,
                ip: device.ip,
                lastSeen: device.lastSeen,
                isCurrent: device.deviceId === req.user?.deviceId
            }));
            res.json({
                devices,
                maxDevices: parseInt(process.env.MAX_DEVICES_PER_USER || '3')
            });
        }
        catch (error) {
            console.error('Get devices error:', error);
            res.status(500).json({ error: 'Internal server error' });
        }
    }
    static async removeDevice(req, res) {
        try {
            if (!req.user) {
                res.status(401).json({ error: 'User not authenticated' });
                return;
            }
            const { deviceId } = req.params;
            if (!deviceId) {
                res.status(400).json({ error: 'Device ID is required' });
                return;
            }
            if (deviceId === req.user.deviceId) {
                res.status(400).json({ error: 'Cannot remove current device. Use logout instead.' });
                return;
            }
            const user = await models_1.User.findById(req.user.id);
            if (!user) {
                res.status(404).json({ error: 'User not found' });
                return;
            }
            user.devices = user.devices.filter(device => device.deviceId !== deviceId);
            await user.save();
            res.json({ message: 'Device removed successfully' });
        }
        catch (error) {
            console.error('Remove device error:', error);
            res.status(500).json({ error: 'Internal server error' });
        }
    }
}
exports.UserController = UserController;
//# sourceMappingURL=userController.js.map