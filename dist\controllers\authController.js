"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const authService_1 = require("../services/authService");
class AuthController {
    static async register(req, res) {
        try {
            const { email, password, fullName } = req.body;
            if (!email || !password || !fullName) {
                res.status(400).json({ error: 'Email, password, and full name are required' });
                return;
            }
            const user = await authService_1.AuthService.registerUser(email, password, fullName);
            res.status(201).json({
                message: 'User registered successfully',
                user: {
                    id: user._id,
                    email: user.email,
                    fullName: user.fullName,
                },
            });
        }
        catch (error) {
            if (error instanceof Error && error.message === 'User already exists') {
                res.status(409).json({ error: error.message });
            }
            else {
                res.status(500).json({ error: 'Internal server error' });
            }
        }
    }
    static async login(req, res) {
        try {
            const { email, password } = req.body;
            if (!email || !password) {
                res.status(400).json({ error: 'Email and password are required' });
                return;
            }
            const userAgent = req.headers['user-agent'] || 'Unknown';
            const ip = req.ip || req.connection.remoteAddress || 'Unknown';
            const { user, tokens } = await authService_1.AuthService.loginUser(email, password, userAgent, ip);
            res.cookie('accessToken', tokens.accessToken, {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'lax',
                maxAge: 15 * 60 * 1000
            });
            res.cookie('refreshToken', tokens.refreshToken, {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'lax',
                maxAge: 7 * 24 * 60 * 60 * 1000
            });
            res.json({
                message: 'Login successful',
                user: {
                    id: user._id,
                    email: user.email,
                    fullName: user.fullName,
                },
                tokens,
            });
        }
        catch (error) {
            if (error instanceof Error && error.message === 'Invalid credentials') {
                res.status(401).json({ error: error.message });
            }
            else {
                res.status(500).json({ error: 'Internal server error' });
            }
        }
    }
    static async refresh(req, res) {
        try {
            let refreshToken = req.body.refreshToken || req.cookies?.refreshToken;
            if (!refreshToken) {
                res.status(400).json({ error: 'Refresh token is required' });
                return;
            }
            const tokens = await authService_1.AuthService.refreshTokens(refreshToken);
            res.cookie('accessToken', tokens.accessToken, {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'lax',
                maxAge: 15 * 60 * 1000
            });
            res.cookie('refreshToken', tokens.refreshToken, {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'lax',
                maxAge: 7 * 24 * 60 * 60 * 1000
            });
            res.json({
                message: 'Tokens refreshed successfully',
                tokens,
            });
        }
        catch (error) {
            res.status(401).json({ error: 'Invalid or expired refresh token' });
        }
    }
    static async logout(req, res) {
        try {
            if (!req.user) {
                res.status(401).json({ error: 'User not authenticated' });
                return;
            }
            await authService_1.AuthService.logoutDevice(req.user.id, req.user.deviceId);
            res.clearCookie('accessToken');
            res.clearCookie('refreshToken');
            res.json({ message: 'Logged out successfully' });
        }
        catch (error) {
            res.status(500).json({ error: 'Internal server error' });
        }
    }
    static async logoutAll(req, res) {
        try {
            if (!req.user) {
                res.status(401).json({ error: 'User not authenticated' });
                return;
            }
            await authService_1.AuthService.logoutAllDevices(req.user.id);
            res.clearCookie('accessToken');
            res.clearCookie('refreshToken');
            res.json({ message: 'Logged out from all devices successfully' });
        }
        catch (error) {
            res.status(500).json({ error: 'Internal server error' });
        }
    }
}
exports.AuthController = AuthController;
//# sourceMappingURL=authController.js.map