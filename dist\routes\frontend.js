"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const frontendController_1 = require("../controllers/frontendController");
const frontendAuth_1 = require("../middlewares/frontendAuth");
const router = (0, express_1.Router)();
router.use(frontendAuth_1.checkFrontendAuth);
router.get('/login', frontendAuth_1.redirectIfAuthenticated, frontendController_1.FrontendController.renderLogin);
router.get('/register', frontendAuth_1.redirectIfAuthenticated, frontendController_1.FrontendController.renderRegister);
router.get('/dashboard', frontendAuth_1.requireFrontendAuth, frontendController_1.FrontendController.renderDashboard);
const categories = ['incomes', 'needs', 'wants', 'investments', 'donations'];
categories.forEach(category => {
    router.get(`/${category}`, frontendAuth_1.requireFrontendAuth, frontendController_1.FrontendController.renderTransactions);
    router.get(`/${category}/new`, frontendAuth_1.requireFrontendAuth, frontendController_1.FrontendController.renderTransactionForm);
    router.get(`/${category}/:id/edit`, frontendAuth_1.requireFrontendAuth, frontendController_1.FrontendController.renderTransactionForm);
});
router.get('/', (req, res) => {
    if (req.isAuthenticated) {
        res.redirect('/dashboard');
    }
    else {
        res.redirect('/login');
    }
});
router.get('*', frontendController_1.FrontendController.render404);
exports.default = router;
//# sourceMappingURL=frontend.js.map